use crate::components::animals::{
    AnimalBaseArmature, AnimalSpeciesModelKey, AnimalSpeciesModelSet, AutoRigColliders, AutoSetupIK, MovementDampingFactor
};
use crate::libraries::blender_ik::{
    Blender<PERSON>k<PERSON>hain, BlenderIkConstraint, BlenderIkJoint, BlenderIkSolution, BlenderIkTarget
};
use crate::libraries::ik_fabrik::chain::IkChain;
use crate::libraries::ik_fabrik::constraint::{SwingConstraint, TwistConstraint};
use crate::libraries::inverse_kinematics;
use crate::libraries::inverse_kinematics::{IkConstraint, IkSwingConstraint};
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::*;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use bevy_descendant_collector::{DescendantCollectorTarget, DescendantLoader};
use bevy_gltf_animation::prelude::{GltfAnimations, GltfSceneRoot};

pub mod animation_blending;
pub mod animation_controller;
pub mod bees;
pub mod system_params;

/// Component to mark an IK target for foot placement
#[derive(Component, Reflect, Debug)]
#[reflect(Component)]
pub struct FootIkTarget {
    /// Which foot this target belongs to
    pub foot_type: FootType,
    /// The entity this target is following (the foot/toe entity)
    pub foot_entity: Entity,
    /// Offset from the foot position when placing on ground
    pub ground_offset: Vec3,
    /// Maximum distance the foot can reach
    pub max_reach: f32,
    /// How smoothly the target moves (0.0 = instant, 1.0 = very smooth)
    pub smoothing: f32,
}

/// Identifies which foot this is for
#[derive(Debug, Clone, Copy, PartialEq, Eq, Reflect)]
pub enum FootType {
    LeftFront,
    RightFront,
    LeftBack,
    RightBack,
}

/// Component for animals that have foot IK enabled
#[derive(Component, Reflect, Debug)]
#[reflect(Component)]
pub struct FootIkController {
    /// IK targets for each foot
    pub foot_targets: Vec<Entity>,
    /// How far ahead to place feet when walking
    pub stride_length: f32,
    /// How high to lift feet when stepping
    pub step_height: f32,
    /// Speed of foot placement
    pub placement_speed: f32,
}

pub fn set_model_for_species(
    mut commands: Commands,
    animals_assets: AnimalsAssetSystemParams,
    query: Query<(Entity, &AnimalSpeciesModelKey), Without<AnimalSpeciesModelSet>>,
) {
    for (entity, species) in query.iter() {
        let Some(model) = animals_assets.get_model(&species.0, None) else {
            log::warn!("Failed to load model for species: {:?}", species.0);
            continue;
        };

        commands
            .entity(entity)
            .insert((AnimalSpeciesModelSet, model));
    }
}

pub fn apply_movement_damping(
    mut query: Query<(&MovementDampingFactor, &mut LinearVelocity)>,
) {
    for (damping_factor, mut linear_velocity) in &mut query {
        linear_velocity.x *= damping_factor.0;
        linear_velocity.z *= damping_factor.0;
    }
}

fn local_transform(child: &GlobalTransform, parent: &GlobalTransform) -> Transform {
    let mats = parent.compute_matrix().inverse() * child.compute_matrix();
    let (scale, rotation, translation) = mats.to_scale_rotation_translation();
    Transform {
        translation,
        rotation,
        scale,
    }
}

/// Convert a quaternion to Euler angles (XYZ order)
/// Returns (x_rotation, y_rotation, z_rotation) in radians
fn quat_to_euler_xyz(quat: Quat) -> (f32, f32, f32) {
    // Convert Bevy Quat to Euler angles using XYZ rotation order
    // This follows the standard aerospace sequence: roll (X), pitch (Y), yaw (Z)

    let w = quat.w;
    let x = quat.x;
    let y = quat.y;
    let z = quat.z;

    // Roll (X-axis rotation)
    let sin_r_cp = 2.0 * (w * x + y * z);
    let cos_r_cp = 1.0 - 2.0 * (x * x + y * y);
    let roll = sin_r_cp.atan2(cos_r_cp);

    // Pitch (Y-axis rotation)
    let sin_p = 2.0 * (w * y - z * x);
    let pitch = if sin_p.abs() >= 1.0 {
        std::f32::consts::FRAC_PI_2.copysign(sin_p) // Use 90 degrees if out of range
    } else {
        sin_p.asin()
    };

    // Yaw (Z-axis rotation)
    let sin_y_cp = 2.0 * (w * z + x * y);
    let cos_y_cp = 1.0 - 2.0 * (y * y + z * z);
    let yaw = sin_y_cp.atan2(cos_y_cp);

    (roll, pitch, yaw)
}

pub fn on_auto_setup_ik2<Armature>(
    mut commands: Commands,
    query: Query<
        (Entity, &GlobalTransform, &Armature),
        (With<AutoSetupIK>, With<Armature>),
    >,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transforms: Query<&GlobalTransform>,
) where
    Armature: Component + AnimalBaseArmature,
{
    for (entity, global_transform, armature) in query.iter() {
        let mut foot_targets = Vec::new();

        let legs = [(armature.get_foot_l(), FootType::LeftBack)];

        for (foot_entity, foot_type) in legs {
            let toe = match foot_type {
                FootType::LeftFront => armature.get_front_toe_l(),
                FootType::RightFront => armature.get_front_toe_r(),
                FootType::LeftBack => armature.get_toe_l(),
                FootType::RightBack => armature.get_toe_r(),
            };

            let foot_gt = transforms.get(foot_entity).unwrap();
            // let foot_aabb = aabbs.get(foot_entity).unwrap();
            let toe_gt = transforms.get(toe).unwrap();

            let foot_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(1.0, 0.0, 0.0),
                        ..default()
                    })),
                    Name::new("Left Foot IK Target"),
                    Transform::from_translation(toe_gt.translation()),
                ))
                .id();

            foot_targets.push(foot_target);

            // First, get all the joints in the chain
            let shin_entity = armature.get_shin_l();
            let thigh_entity = armature.get_thigh_l();
            let hip_entity = armature.get_shoulder_l(); // Or equivalent parent joint
            let root_entity = armature.get_root();

            let pole_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(0.0, 1.0, 0.0),
                        ..default()
                    })),
                    Transform::from_xyz(-1.0, 0.4, -0.2),
                ))
                .id();

            commands.entity(foot_entity).insert(IkConstraint {
                chain_length: 3,
                iterations: 100,
                target: foot_target,
                // pole_target: Some(pole_target),
                // pole_angle: -std::f32::consts::FRAC_PI_2,
                enabled: true,
                ..default()
            });

            commands.entity(entity).remove::<AutoSetupIK>();
        }
    }
}

pub fn on_auto_setup_ik<Armature>(
    mut commands: Commands,
    query: Query<
        (Entity, &GlobalTransform, &Armature),
        (With<AutoSetupIK>, With<Armature>),
    >,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transforms: Query<&GlobalTransform>,
) where
    Armature: Component + AnimalBaseArmature,
{
    for (entity, global_transform, armature) in query.iter() {
        let mut foot_targets = Vec::new();

        let legs = [(armature.get_foot_l(), FootType::LeftBack)];

        for (foot_entity, foot_type) in legs {
            let toe = match foot_type {
                FootType::LeftFront => armature.get_front_toe_l(),
                FootType::RightFront => armature.get_front_toe_r(),
                FootType::LeftBack => armature.get_toe_l(),
                FootType::RightBack => armature.get_toe_r(),
            };

            let foot_gt = transforms.get(foot_entity).unwrap();
            // let foot_aabb = aabbs.get(foot_entity).unwrap();
            let toe_gt = transforms.get(toe).unwrap();

            let foot_target = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(1.0, 0.0, 0.0),
                        ..default()
                    })),
                    Name::new("Left Foot IK Target"),
                    // local_transform(toe_gt, global_transform),
                    Transform::from_translation(toe_gt.translation()),
                    BlenderIkTarget {
                        position: toe_gt.translation(),
                        ..default()
                    },
                    // FootIkTarget {
                    //     foot_type,
                    //     foot_entity,
                    //     ground_offset: Vec3::new(0.0, 0., 0.0), // Slightly above ground
                    //     max_reach: 2.0,
                    //     smoothing: 0.8,
                    // },
                ))
                .id();

            foot_targets.push(foot_target);

            // First, get all the joints in the chain
            let shin_entity = armature.get_shin_l();
            let thigh_entity = armature.get_thigh_l();
            let hip_entity = armature.get_shoulder_l(); // Or equivalent parent joint
            let root_entity = armature.get_root();

            // Calculate constraints based on initial joint rotations
            let thigh_gt = transforms.get(thigh_entity).unwrap();
            let shin_gt = transforms.get(shin_entity).unwrap();
            let foot_gt = transforms.get(foot_entity).unwrap();

            // Extract initial rotation angles for each joint
            let thigh_initial_rotation = thigh_gt.to_scale_rotation_translation().1;
            let shin_initial_rotation = shin_gt.to_scale_rotation_translation().1;
            let foot_initial_rotation = foot_gt.to_scale_rotation_translation().1;

            // Convert quaternions to euler angles for constraint calculation
            let thigh_euler = quat_to_euler_xyz(thigh_initial_rotation);
            let shin_euler = quat_to_euler_xyz(shin_initial_rotation);
            let foot_euler = quat_to_euler_xyz(foot_initial_rotation);

            // Hip/Thigh joint - ball joint with limits based on initial pose
            let thigh_range = std::f32::consts::PI / 3.0; // ±60 degrees from initial
            commands.entity(thigh_entity).insert(
                BlenderIkJoint::default()
                    .with_rotate_x(true)
                    .with_rotate_y(true)
                    .with_rotate_z(true)
                    .with_x_limits(thigh_euler.0 - thigh_range, thigh_euler.0 + thigh_range)
                    .with_y_limits(thigh_euler.1 - thigh_range / 2.0, thigh_euler.1 + thigh_range / 2.0)
                    .with_z_limits(thigh_euler.2 - thigh_range / 3.0, thigh_euler.2 + thigh_range / 3.0)
                    .add_constraint(BlenderIkConstraint::ball_joint(
                        thigh_range,  // Max cone angle based on range
                        thigh_range / 2.0,  // Max twist angle
                    )),
            );

            // Knee/Shin joint - hinge joint that prevents backward bending from initial pose
            let shin_forward_range = std::f32::consts::PI * 0.75; // 135 degrees forward
            let shin_backward_limit = 0.1; // Small backward allowance (5.7 degrees)
            commands.entity(shin_entity).insert(
                BlenderIkJoint::default()
                    .with_rotate_x(true)
                    .with_rotate_y(false)
                    .with_rotate_z(false)
                    .with_x_limits(
                        shin_euler.0 - shin_backward_limit, // Slight backward from initial
                        shin_euler.0 + shin_forward_range,  // Forward bend from initial
                    )
                    .add_constraint(BlenderIkConstraint::hinge_x(
                        shin_euler.0 - shin_backward_limit,  // Minimum angle from initial
                        shin_euler.0 + shin_forward_range,   // Maximum angle from initial
                    )),
            );

            // Ankle/Foot joint - limited ball joint based on initial pose
            let foot_range = std::f32::consts::PI / 4.0; // ±45 degrees from initial
            commands.entity(foot_entity).insert(
                BlenderIkJoint::default()
                    .with_rotate_x(true)
                    .with_rotate_y(true)
                    .with_rotate_z(true)
                    .with_x_limits(foot_euler.0 - foot_range / 2.0, foot_euler.0 + foot_range / 2.0)
                    .with_y_limits(foot_euler.1 - foot_range / 3.0, foot_euler.1 + foot_range / 3.0)
                    .with_z_limits(foot_euler.2 - foot_range, foot_euler.2 + foot_range)
                    .add_constraint(BlenderIkConstraint::ball_joint(
                        foot_range / 2.0,  // Max cone angle
                        foot_range / 4.0,  // Max twist angle
                    )),
            );

            commands.entity(toe).insert(BlenderIkJoint::default());

            // Create IK chain with solution component
            // The joint constraints above should prevent backward bending
            commands.spawn((
                BlenderIkChain {
                    joints: vec![
                        thigh_entity,
                        shin_entity,
                        foot_entity,
                    ],
                    // Adjust joint weights for more natural leg movement
                    // Higher weight on thigh for primary movement, lower on foot for fine adjustment
                    joint_weights: vec![1.0, 0.9, 0.7],
                    target: foot_target,
                    max_iterations: 50, // Reduced iterations for better performance
                    tolerance: 0.01,    // Slightly relaxed tolerance
                    use_sdls: true,     // Use Selectively Damped Least Squares for stability
                    use_tail: false,    // Disable use_tail to prevent extra complexity
                    enabled: true,
                    ..default()
                },
                BlenderIkSolution::default(),
            ));
        }

        // Add the foot IK controller to the animal
        commands.entity(entity).insert(FootIkController {
            foot_targets,
            stride_length: 1.0,
            step_height: 0.3,
            placement_speed: 5.0,
        });

        commands.entity(entity).remove::<AutoSetupIK>();
    }
}

/// System to update foot IK targets based on ground detection and animal movement
pub fn update_foot_ik_targets(
    mut ik_target_query: Query<(&mut Transform, &FootIkTarget)>,
    mut ik_chain_query: Query<&mut IkChain>,
    foot_controller_query: Query<(&FootIkController, &GlobalTransform, &LinearVelocity)>,
    foot_transforms: Query<&GlobalTransform>,
    terrain_system: TerrainSystemParams,
    spatial_query: SpatialQuery,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (controller, animal_transform, velocity) in foot_controller_query.iter() {
        let animal_pos = animal_transform.translation();
        let movement_direction = velocity.0.normalize_or_zero();

        for (i, &target_entity) in controller.foot_targets.iter().enumerate() {
            if let Ok((mut target_transform, foot_target)) =
                ik_target_query.get_mut(target_entity)
            {
                // Calculate base foot position relative to animal body center
                // This provides more stable positioning than using current foot position
                let foot_type = match i {
                    0 => FootType::LeftFront,
                    1 => FootType::RightFront,
                    2 => FootType::LeftBack,
                    3 => FootType::RightBack,
                    _ => FootType::LeftFront,
                };

                // Get rest position offset for this foot type
                let rest_offset = get_foot_rest_offset(foot_type);

                // Apply animal's rotation to the rest offset
                let rotated_offset = animal_transform.rotation() * rest_offset;

                // Calculate stride offset based on movement
                let stride_offset = if velocity.0.length() > 0.1 {
                    movement_direction * controller.stride_length * 0.5
                } else {
                    Vec3::ZERO
                };

                // Calculate desired foot position relative to body center
                let desired_foot_pos = animal_pos + rotated_offset + stride_offset;

                // Perform ground detection using raycast
                let ground_height = detect_ground_height(
                    desired_foot_pos,
                    &spatial_query,
                    &terrain_system,
                );

                // Calculate final target position on ground
                let target_pos = Vec3::new(
                    desired_foot_pos.x,
                    ground_height + foot_target.ground_offset.y,
                    desired_foot_pos.z,
                );

                // Smoothly move target to new position
                let current_pos = target_transform.translation;
                let lerp_factor = (1.0 - foot_target.smoothing)
                    * controller.placement_speed
                    * delta_time.0;
                let new_pos = current_pos.lerp(target_pos, lerp_factor.min(1.0));

                target_transform.translation = new_pos;
            }
        }
    }
}

/// Get the rest position offset for each foot type relative to the animal's body center
fn get_foot_rest_offset(foot_type: FootType) -> Vec3 {
    match foot_type {
        FootType::LeftFront => Vec3::new(0.5, -0.8, 0.8),   // Front left
        FootType::RightFront => Vec3::new(-0.5, -0.8, 0.8), // Front right
        FootType::LeftBack => Vec3::new(0.5, -0.8, -0.8),   // Back left
        FootType::RightBack => Vec3::new(-0.5, -0.8, -0.8), // Back right
    }
}

/// System to update Blender IK targets from foot IK targets
pub fn update_blender_ik_targets(
    foot_targets: Query<&Transform, (With<FootIkTarget>, Changed<Transform>)>,
    mut blender_targets: Query<&mut crate::libraries::blender_ik::BlenderIkTarget>,
) {
    for foot_transform in foot_targets.iter() {
        // Find corresponding Blender IK target
        // This is a simplified approach - in practice you'd want to maintain a mapping
        for mut blender_target in blender_targets.iter_mut() {
            blender_target.position = foot_transform.translation;
        }
    }
}

/// Detect ground height at a given position using raycasting and terrain sampling
fn detect_ground_height(
    position: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> f32 {
    // Start raycast from above the position
    let ray_start = Vec3::new(position.x, position.y + 5.0, position.z);
    let ray_direction = -Dir3::Y;
    let max_distance = 10.0;

    // Perform raycast to find ground
    if let Some(hit) = spatial_query.cast_ray(
        ray_start,
        ray_direction,
        max_distance,
        true,
        &SpatialQueryFilter::default(),
    ) {
        return hit.normal.y;
    }

    // Fallback: try to get terrain height from terrain system
    // This is a simplified approach - you might want to implement proper terrain height sampling
    if terrain_system.is_in_bounds(position) {
        // For now, return a default ground level
        // In a full implementation, you'd sample the terrain height map
        return 0.0;
    }

    // Final fallback
    position.y
}

pub fn on_added_gltf_scene<Animal, T>(
    mut commands: Commands,
    gltf_scenes: Query<
        (Entity, &Children),
        (With<Animal>, With<GltfSceneRoot>, Without<T>),
    >,
    children_query: Query<&Children>,
    name_query: Query<&Name>,
) where
    Animal: Component,
    T: Component + DescendantLoader,
{
    for (entity, children) in gltf_scenes.iter() {
        if children.is_empty() {
            continue;
        }

        for child in children_query.iter_descendants(entity) {
            if let Ok(name) = name_query.get(child) {
                if name.contains("Root") {
                    commands
                        .entity(entity)
                        .insert(DescendantCollectorTarget::<T>::default());

                    return;
                }
            }
        }
    }
}

// pub fn auto_rig_colliders<T>(
//     mut commands: Commands,
//     children: Query<&Children>,
//     mesh_query: Query<(&Aabb, &Mesh3d)>,
//     auto_rig_query: Query<&T, (With<AutoRigColliders>, Added<T>)>,
//     transforms: Query<&Transform>,
//     meshes: Res<Assets<Mesh>>,
// ) where
//     T: Component + DescendantLoader + AnimalBaseArmature,
// {
//     for armature in auto_rig_query.iter() {
//         let mut half_extents = Vec3A::ZERO;
//
//         if let Ok(children) = children.get(armature.get_mesh()) {
//             for child in children.iter() {
//                 if let Ok((aabb, mesh_3d)) = mesh_query.get(child) {
//                     // if let Some(mesh) = meshes.get(&mesh_3d.0) {
//                     //     if let Some(names) = mesh.morph_target_names() {
//                     //         log::info!("Mesh has morph targets: {:?}", names);
//                     //     }
//                     // }
//
//                     half_extents = aabb.half_extents;
//                     break;
//                 }
//             }
//         }
//
//         if half_extents == Vec3A::ZERO {
//             continue;
//         }
//
//         // Auto rotate by 180 degrees on the Y axis
//         if let Ok(base_transform) = transforms.get(armature.get_base()) {
//             commands.entity(armature.get_base()).insert(
//                 base_transform.with_rotation(Quat::from_rotation_y(std::f32::consts::PI)),
//             );
//         }
//
//         commands
//             .entity(armature.get_body())
//             .insert(Collider::cuboid(
//                 half_extents.x * 4.,
//                 half_extents.y * 4.,
//                 half_extents.z * 4.,
//             ));
//     }
// }
